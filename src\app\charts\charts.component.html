<mat-sidenav-container class="sidenav-container">
  <!-- Sidebar -->
  <mat-sidenav #sidenav mode="side" opened class="sidenav" [ngClass]="{'sidenav-collapsed': !sidenav.opened}">
    <div class="sidenav-header">
      <img src="assets/logo.png" alt="App Logo" class="sidenav-logo" [ngClass]="{'logo-collapsed': !sidenav.opened}" />
    </div>
    <mat-nav-list>
      <a mat-list-item routerLink="/dashboard" routerLinkActive="active">
        <mat-icon>dashboard</mat-icon>
        <span class="nav-text">Dashboard</span>
      </a>
      <a mat-list-item routerLink="/other" routerLinkActive="active">
        <mat-icon>pages</mat-icon>
        <span class="nav-text">Other Page</span>
      </a>
      <a mat-list-item (click)="logout()" class="logout-item">
        <mat-icon>logout</mat-icon>
        <span class="nav-text">Logout</span>
      </a>
    </mat-nav-list>
  </mat-sidenav>

  <mat-sidenav-content>
    <div class="dashboard-container">
      <mat-toolbar color="primary" class="toolbar">
        <button mat-icon-button (click)="sidenav.toggle()">
          <mat-icon>menu</mat-icon>
        </button>
        <span>Marketing Dashboard</span>
      </mat-toolbar>

      <div class="content">
        <div class="chart-grid">
          <!-- Open Rate by Campaign -->
          <div class="chart-card">
            <mat-card
              *ngIf="
                (openRateChartData.labels ?? []).length > 0;
                else noOpenRate
              "
            >
              <mat-card-header
                ><mat-card-title
                  >Open Rate by Campaign</mat-card-title
                ></mat-card-header
              >
              <mat-card-content>
                <div class="chart-container">
                  <canvas
                    baseChart
                    [data]="openRateChartData"
                    [options]="openRateChartOptions"
                    [type]="'line'"
                  ></canvas>
                </div>
              </mat-card-content>
            </mat-card>
            <ng-template #noOpenRate>
              <mat-card
                ><mat-card-content
                  ><p>
                    No data available for Open Rate by Campaign.
                  </p></mat-card-content
                ></mat-card
              >
            </ng-template>
          </div>

          <!-- Delivery Rate by Gender -->
          <div class="chart-card">
            <mat-card
              *ngIf="
                (deliveryRateChartData.labels ?? []).length > 0;
                else noDeliveryRate
              "
            >
              <mat-card-header
                ><mat-card-title
                  >Delivery Rate by Gender</mat-card-title
                ></mat-card-header
              >
              <mat-card-content>
                <div class="chart-container">
                  <canvas
                    baseChart
                    [data]="deliveryRateChartData"
                    [options]="deliveryRateChartOptions"
                    [type]="'bar'"
                  ></canvas>
                </div>
              </mat-card-content>
            </mat-card>
            <ng-template #noDeliveryRate>
              <mat-card
                ><mat-card-content
                  ><p>
                    No data available for Delivery Rate by Gender.
                  </p></mat-card-content
                ></mat-card
              >
            </ng-template>
          </div>

          <!-- Spend by Age Group -->
          <div class="chart-card">
            <mat-card *ngIf="isLoadingSpendByAge">
              <mat-card-content
                ><p>Loading Spend by Age Group...</p></mat-card-content
              >
            </mat-card>
            <mat-card
              *ngIf="
                !isLoadingSpendByAge &&
                  (spendByAgeChartData.labels ?? []).length > 0;
                else noSpendByAge
              "
            >
              <mat-card-header
                ><mat-card-title
                  >Spend by Age Group</mat-card-title
                ></mat-card-header
              >
              <mat-card-content>
                <div class="chart-container">
                  <canvas
                    baseChart
                    [data]="spendByAgeChartData"
                    [options]="spendByAgeChartOptions"
                    [type]="'doughnut'"
                  ></canvas>
                </div>
              </mat-card-content>
            </mat-card>
            <ng-template #noSpendByAge>
              <mat-card
                ><mat-card-content
                  ><p>
                    No data available for Spend by Age Group.
                  </p></mat-card-content
                ></mat-card
              >
            </ng-template>
          </div>
          <div class="chart-card">
            <mat-card *ngIf="(ageDayOpenChartData.labels ?? []).length > 0; else noUniqueViews">
              <mat-card-header>
                <mat-card-title>Unique Views by Day of Week and Age</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="chart-container">
                  <canvas baseChart
                          [data]="ageDayOpenChartData"
                          [options]="ageDayOpenChartOptions"
                          [type]="'bar'">
                  </canvas>
                </div>
              </mat-card-content>
            </mat-card>
            <ng-template #noUniqueViews>
              <mat-card><mat-card-content><p>No data available for Unique Views by Day and Age.</p></mat-card-content></mat-card>
            </ng-template>
          </div>
          <div class="chart-card">
            <mat-card *ngIf="(mixedChartData.labels ?? []).length > 0; else noRadarData">
              <mat-card-header><mat-card-title>Unique Views by Time of Day and Age</mat-card-title></mat-card-header>
              <mat-card-content>
                <div class="chart-container">
                  <canvas baseChart
                  [data]="mixedChartData"
                  [options]="mixedChartOptions">
                         
                  </canvas>
                </div>
              </mat-card-content>
            </mat-card>
            <ng-template #noRadarData>
              <mat-card><mat-card-content><p>No data available for Unique Views by Time of Day and Age.</p></mat-card-content></mat-card>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>