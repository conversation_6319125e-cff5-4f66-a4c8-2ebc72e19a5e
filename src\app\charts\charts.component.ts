import { Component, OnInit } from '@angular/core';
import { ChartData, ChartOptions } from 'chart.js';
import { DashboardService } from '../services/dashboard.service';
import { AuthService } from '../services/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-charts',
  templateUrl: './charts.component.html',
  styleUrls: ['./charts.component.css']
})
export class ChartsComponent implements OnInit {
  openRateChartData: ChartData<'line'> = { labels: [], datasets: [{ data: [], label: '' }] };
  deliveryRateChartData: ChartData<'bar'> = { labels: [], datasets: [{ data: [], label: '' }] };
  spendByAgeChartData: ChartData<'doughnut'> = { labels: [], datasets: [{ data: [], label: '' }] };
  ageDayOpenChartData: ChartData<'bar'> = { labels: [], datasets: [] };
  mixedChartData: ChartData<'bar' | 'line'> = { labels: [], datasets: [] };
  
  isLoadingSpendByAge: boolean = true;

  openRateChartOptions: ChartOptions<'line'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: true, position: 'top' },
      tooltip: { enabled: true }
    },
    scales: {
      y: { beginAtZero: true, title: { display: true, text: 'Open Rate (%)' } },
      x: { title: { display: true, text: 'Campaign' } }
    }
  };

  deliveryRateChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      tooltip: { enabled: true }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: { display: true, text: 'Delivery Rate (%)' }
      },
      x: {
        title: { display: true, text: 'Gender' }
      }
    }
  };
  

  spendByAgeChartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'right' },
      tooltip: { enabled: true }
    }
  };
  ageDayOpenChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'top' },
      tooltip: { enabled: true }
    },
    scales: {
      x: { title: { display: true, text: 'Day of Week' }},
      y: { beginAtZero: true, title: { display: true, text: 'Number of Opens' }}
    }
  };
  mixedChartOptions: ChartOptions<'bar' | 'line'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'top' },
      tooltip: { enabled: true }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: { display: true, text: 'Number of Opens' }
      },
      x: {
        title: { display: true, text: 'Time of Day' }
      }
    }
  };
  constructor(private dashboardService: DashboardService,private authService: AuthService, private router: Router) {}

  ngOnInit(): void {
    this.loadOpenRateByCampaign();
    this.loadDeliveryRateByGender();
    this.loadSpendByAge();
    this.loadAgeDayOpenData();
    this.loadMixedChartData();
  }
  loadMixedChartData(): void {
    this.dashboardService.GetTODEOAGE().subscribe({
      next: (data) => {
        const timeLabels = ['1. Early Morning', '2. Morning', '3. Afternoon', '4. Evening'];
        this.mixedChartData.labels = timeLabels;
  
        const validData = data.filter((d: any) => d['[Age Bands].[Age].[Age].[MEMBER_CAPTION]'] && typeof d['[Age Bands].[Age].[Age].[MEMBER_CAPTION]'] === 'string');
  
        const datasets = validData.map((ageData: any, index: number) => ({
          label: ageData['[Age Bands].[Age].[Age].[MEMBER_CAPTION]'],
          data: [
            ageData['[Time Of Day Email Opened].[TimeDEO].&[1.]'] ?? 0,
            ageData['[Time Of Day Email Opened].[TimeDEO].&[2.]'] ?? 0,
            ageData['[Time Of Day Email Opened].[TimeDEO].&[3.]'] ?? 0,
            ageData['[Time Of Day Email Opened].[TimeDEO].&[4.]'] ?? 0
          ],
          type: index % 2 === 0 ? 'bar' : 'line', // Alternate types for fun; or you can fix to 'bar' or 'line'
          backgroundColor: this.getTransparentRandomColor(),
          borderColor: this.getRandomColor(),
          fill: false
        }));
  
        this.mixedChartData.datasets = datasets;
      },
      error: () => {
        this.mixedChartData = { labels: [], datasets: [] };
      }
    });
  }
  getTransparentRandomColor(): string {
    const color = this.getRandomColor();
    return color + '33'; // add 20% opacity
  }
  loadAgeDayOpenData(): void {
    this.dashboardService.GetTODEOperAgeandDOWEO().subscribe({
      next: (data) => {
        // Define day labels
        const dayLabels = ['Sunday','Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        this.ageDayOpenChartData.labels = dayLabels;
  
        // Filter out the total row (empty age)
        const filteredData = data.filter((d: any) => d['[Age Bands].[Age].[Age].[MEMBER_CAPTION]']);
  
        // Create dataset for each age group
        const datasets = filteredData.map((ageData: any) => {
          // Check if the label is an object and replace it with a meaningful name if necessary
          let label = ageData['[Age Bands].[Age].[Age].[MEMBER_CAPTION]'];
          if (typeof label === 'object') {
            label = 'total'; // Replace the object with a fallback name like "Unknown"
          }
          
          return {
            label: label || 'Unknown',
            data: [
              ageData['[Day Of Week Email Opened].[DayWEO].&[1.]'] ?? 0,
              ageData['[Day Of Week Email Opened].[DayWEO].&[2.]'] ?? 0,
              ageData['[Day Of Week Email Opened].[DayWEO].&[3.]'] ?? 0,
              ageData['[Day Of Week Email Opened].[DayWEO].&[4.]'] ?? 0,
              ageData['[Day Of Week Email Opened].[DayWEO].&[5.]'] ?? 0,
              ageData['[Day Of Week Email Opened].[DayWEO].&[6.]'] ?? 0,
              ageData['[Day Of Week Email Opened].[DayWEO].&[7.]'] ?? 0
            ],
            backgroundColor: Array(7).fill(null).map(() => this.getRandomColor())
          };
        });
  
        // Assign datasets to chart data
        this.ageDayOpenChartData.datasets = datasets;
      },
      error: () => {
        this.ageDayOpenChartData = { labels: [], datasets: [] };
      }
    });
  }
  
  
  // Helper function to generate random colors
  getRandomColor(): string {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  }  loadOpenRateByCampaign(): void {
    this.dashboardService.getOpenRateByCampaign().subscribe({
      next: (data) => {
        const valid = data.filter((d: any) =>
          d['[Measures].[OpenRate]'] != null &&
          d['[Compaigns].[Campaign].[Campaign].[MEMBER_CAPTION]'] != null
        );
        const labels = valid.map((d: any) =>
          String(d['[Compaigns].[Campaign].[Campaign].[MEMBER_CAPTION]'])
        );
        const values = valid.map((d: any) =>
          Number(d['[Measures].[OpenRate]'] * 100)
        );

        this.openRateChartData = {
          labels,
          datasets: [{
            data: values,
            label: 'Open Rate (%)',
            borderColor: '#42A5F5',
            backgroundColor: 'rgba(66,165,245,0.3)',
            fill: true,
            tension: 0.4
          }]
        };
      },
      error: () => {
        this.openRateChartData = { labels: [], datasets: [{ data: [], label: '' }] };
      }
    });
  }

  loadDeliveryRateByGender(): void {
    this.dashboardService.getDeliveryRateByGender().subscribe({
      next: (data) => {
        const valid = data.filter((d: any) =>
          d['[Measures].[DeliveryRate]'] != null &&
          (d['[Genders].[Gender].[Gender].[MEMBER_CAPTION]'] === 'Male' ||
           d['[Genders].[Gender].[Gender].[MEMBER_CAPTION]'] === 'Female')
        );
  
        const labels = valid.map((d: any) =>
          String(d['[Genders].[Gender].[Gender].[MEMBER_CAPTION]'])
        );
        const values = valid.map((d: any) =>
          Number(d['[Measures].[DeliveryRate]'] * 100)
        );
  
        this.deliveryRateChartData = {
          labels,
          datasets: [{
            data: values,
            label: 'Delivery Rate (%)',
            backgroundColor: ['#4BC0C0', '#FF9F40']
          }]
        };
      },
      error: () => {
        this.deliveryRateChartData = { labels: [], datasets: [{ data: [], label: '' }] };
      }
    });
  }

  loadSpendByAge(): void {
    this.isLoadingSpendByAge = true;
    this.dashboardService.getNbSpendByAge().subscribe({
      next: (data) => {
        const valid = data.filter((d: any) =>
          typeof d['[Measures].[Spend]'] === 'number' &&
          typeof d['[Age Bands].[Age].[Age].[MEMBER_CAPTION]'] === 'string' &&
          d['[Age Bands].[Age].[Age].[MEMBER_CAPTION]'] !== 'Unknown'
        );
  
        if (valid.length > 0) {
          const labels = valid.map((d: any) => d['[Age Bands].[Age].[Age].[MEMBER_CAPTION]']);
          const values = valid.map((d: any) => d['[Measures].[Spend]']);
  
          this.spendByAgeChartData = {
            labels,
            datasets: [
              {
                label: 'Spend by Age Group',
                data: values,
                backgroundColor: [
                  '#FF6384',
                  '#36A2EB',
                  '#FFCE56',
                  '#8E44AD',
                  '#2ECC71',
                  '#E67E22',
                  '#3498DB',
                  '#F1C40F'
                ]
              }
            ]
          };
        } else {
          this.spendByAgeChartData = { labels: [], datasets: [{ data: [], label: '' }] };
        }
  
        this.isLoadingSpendByAge = false;
      },
      error: () => {
        this.spendByAgeChartData = { labels: [], datasets: [{ data: [], label: '' }] };
        this.isLoadingSpendByAge = false;
      }
    });
  }
  logout() {
    this.authService.logout();
    this.router.navigate(['/login']);
  }
}
