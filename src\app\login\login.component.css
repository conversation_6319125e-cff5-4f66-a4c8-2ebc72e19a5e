@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css");

/* CSS Custom Properties for awesome theming */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

  --primary-color: #667eea;
  --primary-dark: #5a6fd8;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;

  --text-primary: #1a202c;
  --text-secondary: #4a5568;
  --text-light: #a0aec0;
  --text-white: #ffffff;

  --background-primary: #ffffff;
  --background-secondary: #f7fafc;
  --background-dark: #2d3748;

  --border-color: #e2e8f0;
  --border-focus: #667eea;

  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
}

/* Main Container - Split Screen Layout */
.login-container {
  display: flex;
  min-height: 100vh;
  background: var(--background-secondary);
}

/* Left Side - Branding & Image */
.login-left {
  flex: 1;
  background: var(--primary-gradient);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  color: var(--text-white);
}

.login-left::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.brand-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 500px;
  padding: 2rem;
  animation: fadeInLeft 0.8s ease-out;
}

.brand-logo {
  margin-bottom: 2rem;
}

.brand-logo img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  box-shadow: var(--shadow-xl);
  border: 4px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease;
}

.brand-logo img:hover {
  transform: scale(1.1) rotate(5deg);
}

.brand-title {
  font-size: 3rem;
  font-weight: 800;
  margin: 0 0 1rem 0;
  background: linear-gradient(45deg, #ffffff, #f0f8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.02em;
}

.brand-subtitle {
  font-size: 1.25rem;
  margin: 0 0 3rem 0;
  opacity: 0.9;
  font-weight: 300;
  line-height: 1.6;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(10px);
}

.feature-item i {
  font-size: 1.5rem;
  color: #ffffff;
  min-width: 24px;
}

.feature-item span {
  font-size: 1.1rem;
  font-weight: 500;
}

/* Decorative Elements */
.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 5%;
  animation-delay: 2s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  top: 60%;
  right: 20%;
  animation-delay: 4s;
}

/* Right Side - Login Form */
.login-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: var(--background-primary);
  position: relative;
}

.login-form-container {
  width: 100%;
  max-width: 400px;
  animation: fadeInRight 0.8s ease-out;
}

.form-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.form-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-header p {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
  font-weight: 400;
}

/* Form Styles */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1.25rem;
  color: var(--text-light);
  font-size: 1.1rem;
  z-index: 2;
  transition: all 0.3s ease;
}

.input-wrapper input {
  width: 100%;
  padding: 1.25rem 1.25rem 1.25rem 3rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-xl);
  font-size: 1rem;
  background: var(--background-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  color: var(--text-primary);
  font-weight: 500;
}

.input-wrapper input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.input-wrapper input:focus + label,
.input-wrapper input:not(:placeholder-shown) + label {
  transform: translateY(-2.5rem) translateX(-0.5rem) scale(0.85);
  color: var(--border-focus);
  background: var(--background-primary);
  padding: 0 0.75rem;
  font-weight: 600;
}

.input-wrapper input:focus ~ .input-icon {
  color: var(--border-focus);
  transform: scale(1.1);
}

.input-wrapper label {
  position: absolute;
  left: 3rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  font-size: 1rem;
  font-weight: 500;
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.input-wrapper input.invalid {
  border-color: var(--error-color);
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

.input-wrapper input.invalid ~ .input-icon {
  color: var(--error-color);
}
/* Password wrapper specific styles */
.password-wrapper .toggle-password {
  position: absolute;
  right: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  font-size: 1.1rem;
  padding: 0.75rem;
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  z-index: 3;
}

.password-wrapper .toggle-password:hover {
  color: var(--border-focus);
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-50%) scale(1.1);
}

/* Error messages */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--error-color);
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(239, 68, 68, 0.05);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--error-color);
}

.error-message i {
  font-size: 1rem;
  min-width: 16px;
}

.global-error {
  background: rgba(239, 68, 68, 0.1);
  border: 2px solid rgba(239, 68, 68, 0.2);
  border-radius: var(--radius-xl);
  padding: 1.25rem;
  margin: 1.5rem 0 0 0;
  animation: shake 0.5s ease-in-out;
}

/* Login button */
.login-btn {
  width: 100%;
  padding: 1.25rem;
  background: var(--primary-gradient);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-xl);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
  margin-top: 1rem;
}

.login-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: var(--shadow-2xl);
}

.login-btn:hover:not(:disabled)::before {
  left: 100%;
}

.login-btn:active:not(:disabled) {
  transform: translateY(-1px);
}

.login-btn:disabled {
  background: var(--text-light);
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
}

.btn-content,
.loading-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
}

/* Spinner for loading state */
.spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid var(--text-white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Error container */
.error-container {
  margin-top: 1rem;
}

/* Animations */
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Responsive design */
@media (max-width: 1024px) {
  .login-left {
    display: none;
  }

  .login-right {
    flex: none;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .login-right {
    padding: 1.5rem;
  }

  .login-form-container {
    max-width: 100%;
  }

  .form-header h2 {
    font-size: 2rem;
  }

  .brand-title {
    font-size: 2.5rem;
  }

  .input-wrapper input {
    padding: 1rem 1rem 1rem 2.75rem;
  }

  .input-wrapper label {
    left: 2.75rem;
  }

  .input-icon {
    left: 1rem;
  }
}

@media (max-width: 480px) {
  .login-right {
    padding: 1rem;
  }

  .form-header {
    margin-bottom: 2rem;
  }

  .form-header h2 {
    font-size: 1.75rem;
  }

  .login-form {
    gap: 1.5rem;
  }

  .input-wrapper input {
    padding: 1rem 1rem 1rem 2.5rem;
    font-size: 0.95rem;
  }

  .input-wrapper label {
    left: 2.5rem;
    font-size: 0.95rem;
  }

  .input-icon {
    left: 0.875rem;
    font-size: 1rem;
  }

  .login-btn {
    padding: 1rem;
    font-size: 1rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .input-wrapper input {
    border-width: 3px;
  }

  .login-btn {
    border: 2px solid var(--text-primary);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .floating-shape {
    animation: none;
  }
}

/* Form options (Remember me & Forgot password) */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.checkbox-container input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all 0.3s ease;
  background: var(--background-white);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
  background: var(--border-focus);
  border-color: var(--border-focus);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
  content: "";
  position: absolute;
  left: 0.25rem;
  top: 0.125rem;
  width: 0.375rem;
  height: 0.625rem;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.forgot-password {
  color: var(--border-focus);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Login button */
.login-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-dark) 100%
  );
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.login-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.login-btn:hover:not(:disabled)::before {
  left: 100%;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.login-btn:active:not(:disabled) {
  transform: translateY(0);
}

.login-btn:disabled {
  background: var(--text-light);
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
}

.btn-content,
.loading-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Spinner for loading state */
.spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Divider */
.divider {
  position: relative;
  text-align: center;
  margin: 1.5rem 0;
}

.divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border-color);
}

.divider span {
  background: var(--background-white);
  color: var(--text-light);
  padding: 0 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Social login buttons */
.social-login {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.social-btn {
  width: 100%;
  padding: 0.875rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--background-white);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.social-btn:hover {
  border-color: var(--border-focus);
  background: rgba(59, 130, 246, 0.05);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.google-btn:hover {
  border-color: #db4437;
  background: rgba(219, 68, 55, 0.05);
  color: #db4437;
}

.microsoft-btn:hover {
  border-color: #00a1f1;
  background: rgba(0, 161, 241, 0.05);
  color: #00a1f1;
}

/* Error container */
.error-container {
  padding: 0 2rem 1rem;
}

/* Card footer */
.card-footer {
  text-align: center;
  padding: 1.5rem 2rem 2rem;
  background: var(--background-light);
  border-top: 1px solid var(--border-color);
}

.card-footer p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.signup-link {
  color: var(--border-focus);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.signup-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .login-background {
    padding: 0.5rem;
  }

  .card-header {
    padding: 2rem 1.5rem 1rem;
  }

  .login-form {
    padding: 1rem 1.5rem;
  }

  .card-footer {
    padding: 1rem 1.5rem 1.5rem;
  }

  .card-header h2 {
    font-size: 1.5rem;
  }

  .social-login {
    gap: 0.5rem;
  }

  .form-options {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

@media (max-width: 360px) {
  .input-wrapper input {
    padding: 0.875rem 0.875rem 0.875rem 2.5rem;
  }

  .input-wrapper label {
    left: 2.5rem;
  }

  .input-icon {
    left: 0.875rem;
  }
}
