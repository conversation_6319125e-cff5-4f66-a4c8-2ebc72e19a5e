@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css");

/* CSS Custom Properties for consistent theming */
:root {
  --primary-color: #667eea;
  --primary-dark: #5a6fd8;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --background-light: #f9fafb;
  --background-white: #ffffff;
  --border-color: #e5e7eb;
  --border-focus: #3b82f6;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* Background with gradient */
.login-background {
  min-height: 100vh;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--secondary-color) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

.login-background::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.1;
}

.login-container {
  width: 100%;
  max-width: 420px;
  position: relative;
  z-index: 1;
}

.login-card {
  background: var(--background-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  animation: slideInUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Card Header */
.card-header {
  text-align: center;
  padding: 2.5rem 2rem 1.5rem;
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.05) 0%,
    rgba(118, 75, 162, 0.05) 100%
  );
}

.logo {
  margin-bottom: 1.5rem;
}

.logo img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
}

.logo img:hover {
  transform: scale(1.05);
}

.card-header h2 {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.025em;
}

.subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
  font-weight: 400;
}

/* Form Styles */
.login-form {
  padding: 1.5rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  color: var(--text-light);
  font-size: 1rem;
  z-index: 2;
  transition: color 0.3s ease;
}

.input-wrapper input {
  width: 100%;
  padding: 1rem 1rem 1rem 2.75rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  background: var(--background-white);
  transition: all 0.3s ease;
  box-sizing: border-box;
  color: var(--text-primary);
}

.input-wrapper input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-wrapper input:focus + label,
.input-wrapper input.filled + label {
  transform: translateY(-1.5rem) scale(0.85);
  color: var(--border-focus);
  background: var(--background-white);
  padding: 0 0.5rem;
}

.input-wrapper input:focus ~ .input-icon {
  color: var(--border-focus);
}

.input-wrapper label {
  position: absolute;
  left: 2.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  font-size: 1rem;
  font-weight: 500;
  pointer-events: none;
  transition: all 0.3s ease;
  z-index: 1;
}

.input-wrapper input.invalid {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-wrapper input.invalid ~ .input-icon {
  color: var(--error-color);
}
/* Password wrapper specific styles */
.password-wrapper {
  position: relative;
}

.password-wrapper .toggle-password {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  font-size: 1rem;
  padding: 0.5rem;
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
  z-index: 3;
}

.password-wrapper .toggle-password:hover {
  color: var(--border-focus);
  background: rgba(59, 130, 246, 0.1);
}

/* Error messages */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--error-color);
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 0.25rem;
}

.error-message i {
  font-size: 0.75rem;
}

.global-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: var(--radius-md);
  padding: 1rem;
  margin: 0;
}

/* Form options (Remember me & Forgot password) */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.checkbox-container input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all 0.3s ease;
  background: var(--background-white);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
  background: var(--border-focus);
  border-color: var(--border-focus);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
  content: "";
  position: absolute;
  left: 0.25rem;
  top: 0.125rem;
  width: 0.375rem;
  height: 0.625rem;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.forgot-password {
  color: var(--border-focus);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Login button */
.login-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-dark) 100%
  );
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.login-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.login-btn:hover:not(:disabled)::before {
  left: 100%;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.login-btn:active:not(:disabled) {
  transform: translateY(0);
}

.login-btn:disabled {
  background: var(--text-light);
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
}

.btn-content,
.loading-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Spinner for loading state */
.spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Divider */
.divider {
  position: relative;
  text-align: center;
  margin: 1.5rem 0;
}

.divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border-color);
}

.divider span {
  background: var(--background-white);
  color: var(--text-light);
  padding: 0 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Social login buttons */
.social-login {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.social-btn {
  width: 100%;
  padding: 0.875rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--background-white);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.social-btn:hover {
  border-color: var(--border-focus);
  background: rgba(59, 130, 246, 0.05);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.google-btn:hover {
  border-color: #db4437;
  background: rgba(219, 68, 55, 0.05);
  color: #db4437;
}

.microsoft-btn:hover {
  border-color: #00a1f1;
  background: rgba(0, 161, 241, 0.05);
  color: #00a1f1;
}

/* Error container */
.error-container {
  padding: 0 2rem 1rem;
}

/* Card footer */
.card-footer {
  text-align: center;
  padding: 1.5rem 2rem 2rem;
  background: var(--background-light);
  border-top: 1px solid var(--border-color);
}

.card-footer p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.signup-link {
  color: var(--border-focus);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.signup-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .login-background {
    padding: 0.5rem;
  }

  .card-header {
    padding: 2rem 1.5rem 1rem;
  }

  .login-form {
    padding: 1rem 1.5rem;
  }

  .card-footer {
    padding: 1rem 1.5rem 1.5rem;
  }

  .card-header h2 {
    font-size: 1.5rem;
  }

  .social-login {
    gap: 0.5rem;
  }

  .form-options {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

@media (max-width: 360px) {
  .input-wrapper input {
    padding: 0.875rem 0.875rem 0.875rem 2.5rem;
  }

  .input-wrapper label {
    left: 2.5rem;
  }

  .input-icon {
    left: 0.875rem;
  }
}
