@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css");
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");

.login-container {
  max-width: 400px;
  margin: 100px auto;
  padding: 2.5rem;
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.6s ease-in-out;
  font-family: 'Inter', sans-serif;

  @media (max-width: 480px) {
    margin: 40px 20px;
    padding: 2rem;
  }
}

.logo {
  text-align: center;
  margin-bottom: 1.5rem;

  img {
    max-width: 60px;
    height: auto;
    transition: transform 0.3s ease;
  }

  img:hover {
    transform: scale(1.1);
  }
}

.login-container h2 {
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 700;
  color: #1a202c;
  letter-spacing: -0.02em;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.input-group {
  display: flex;
  flex-direction: column;

  label {
    font-size: 0.95rem;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-weight: 500;
    transition: color 0.3s ease;
  }

  input {
    width: 100%;
    padding: 0.85rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    color: #1a202c;
    background: #f7fafc;
    transition: all 0.3s ease;
    box-sizing: border-box;

    &:focus {
      outline: none;
      border-color: #3182ce;
      background: #fff;
      box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
    }

    &.invalid {
      border-color: #f56565;
      background: #fff5f5;
    }
  }
}

.password-wrapper {
  position: relative;
  width: 100%;

  input {
    padding-right: 3rem;
  }

  .toggle-password {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    cursor: pointer;
    color: #718096;
    font-size: 1.1rem;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.3s ease;

    &:hover {
      color: #2b6cb0;
    }
  }
}

.forgot-password {
  text-align: right;
  margin-top: -0.5rem;
  margin-bottom: 1rem;

  a {
    font-size: 0.9rem;
    color: #3182ce;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
      color: #2b6cb0;
    }
  }
}

.login-button {
  padding: 0.85rem;
  background: linear-gradient(90deg, #3182ce, #2b6cb0);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 8px rgba(49, 130, 206, 0.2);

  &:hover:not(:disabled) {
    background: linear-gradient(90deg, #2b6cb0, #2c5282);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.3);
  }

  &:disabled {
    background: #e2e8f0;
    color: #a0aec0;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
  }
}

.spinner {
  border: 3px solid #edf2f7;
  border-top: 3px solid #fff;
  border-radius: 50%;
  width: 1.4rem;
  height: 1.4rem;
  animation: spin 0.8s linear infinite;
}

.error {
  color: #f56565;
  text-align: center;
  margin-top: 1rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.signup-link {
  text-align: center;
  margin-top: 1.5rem;
  font-size: 0.9rem;
  color: #4a5568;

  a {
    color: #3182ce;
    text-decoration: none;
    font-weight: 600;

    &:hover {
      text-decoration: underline;
      color: #2b6cb0;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}