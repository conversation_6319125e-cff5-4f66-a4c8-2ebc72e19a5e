<mat-sidenav-container class="sidenav-container">
  <!-- Sidebar -->
  <mat-sidenav #sidenav mode="side" opened class="sidenav" [ngClass]="{'sidenav-collapsed': !sidenav.opened}">
    <div class="sidenav-header">
      <img src="assets/logo.png" alt="App Logo" class="sidenav-logo" [ngClass]="{'logo-collapsed': !sidenav.opened}" />
    </div>
    <mat-nav-list>
      <a mat-list-item routerLink="/dashboard" routerLinkActive="active">
        <mat-icon>dashboard</mat-icon>
        <span class="nav-text">Dashboard</span>
      </a>
      <a mat-list-item routerLink="/other" routerLinkActive="active">
        <mat-icon>pages</mat-icon>
        <span class="nav-text">Other Page</span>
      </a>
      <a mat-list-item (click)="logout()" class="logout-item">
        <mat-icon>logout</mat-icon>
        <span class="nav-text">Logout</span>
      </a>
    </mat-nav-list>
  </mat-sidenav>

  <!-- Main Content -->
  <mat-sidenav-content>
    <div class="dashboard-container">
      <!-- Toolbar -->
      <mat-toolbar color="primary">
        <button mat-icon-button (click)="sidenav.toggle()">
          <mat-icon>menu</mat-icon>
        </button>
        <span>Marketing Dashboard</span>
      </mat-toolbar>

      <!-- Charts and Cards -->
      <div class="content">
        <!-- KPI Cards -->
        <div class="kpi-cards">
          <mat-card class="kpi-card">
            <mat-icon>send</mat-icon>
            <mat-card-title>Nb Sent</mat-card-title>
            <mat-card-content>{{ nbSent }}</mat-card-content>
          </mat-card>
          <mat-card class="kpi-card">
            <mat-icon>check_circle</mat-icon>
            <mat-card-title>Nb Deliv</mat-card-title>
            <mat-card-content>{{ nbDeliv }}</mat-card-content>
          </mat-card>
          <mat-card class="kpi-card">
            <mat-icon>local_shipping</mat-icon>
            <mat-card-title>Delivery Rate</mat-card-title>
            <mat-card-content>{{ deliveryRate }}</mat-card-content>
          </mat-card>
          <mat-card class="kpi-card">
            <mat-icon>visibility</mat-icon>
            <mat-card-title>Open Rate</mat-card-title>
            <mat-card-content>{{ openRate }}</mat-card-content>
          </mat-card>
          <mat-card class="kpi-card">
            <mat-icon>person</mat-icon>
            <mat-card-title>Visited</mat-card-title>
            <mat-card-content>{{ visited }}</mat-card-content>
          </mat-card>
          <mat-card class="kpi-card">
            <mat-icon>attach_money</mat-icon>
            <mat-card-title>Spend</mat-card-title>
            <mat-card-content>{{ spend }}</mat-card-content>
          </mat-card>
        </div>
        <!-- Campaign Stats Chart -->
        <mat-card class="chart-card">
          <mat-card-header>
            <mat-card-title>Nb Sent & Nb Deliv per Campaign</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="chart-container">
              <canvas
                baseChart
                [datasets]="campaignChartData.datasets"
                [labels]="campaignChartData.labels"
                [options]="campaignChartOptions"
                [legend]="true"
                [type]="'bar'"
              ></canvas>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Spend by Gender Chart -->
        <mat-card class="chart-card">
          <mat-card-header>
            <mat-card-title>Spend per Gender</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="chart-container">
              <canvas
                baseChart
                [datasets]="spendByGenderChartData.datasets"
                [labels]="spendByGenderChartData.labels"
                [options]="spendByGenderChartOptions"
                [legend]="true"
                [type]="'bar'"
              ></canvas>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>