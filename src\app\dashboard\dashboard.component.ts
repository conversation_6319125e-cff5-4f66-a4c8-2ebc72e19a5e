import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { ChartConfiguration, ChartData } from 'chart.js';
import { DashboardService } from '../services/dashboard.service';
import { AuthService } from '../services/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {
  campaignChartData: ChartData<'bar'> = { labels: [], datasets: [] };
  campaignChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    scales: { y: { beginAtZero: true } }
  };

  spendByGenderChartData: ChartData<'bar'> = { labels: [], datasets: [] };
  spendByGenderChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    scales: { y: { beginAtZero: true } }
  };

  nbSent: string = '';
  nbDeliv: string = '';
  deliveryRate: string = '';
  openRate: string = '';
  visited: string = '';
  spend: string = '';

  constructor(private dashboardService: DashboardService,private authService: AuthService, private router: Router) {}

  ngOnInit(): void {
    this.loadCampaignStats();
    this.loadSpendByGender();
    this.loadKPIs();
  }

  // Utility function to format numbers (e.g., 164605 → "164,61K")
  formatNumber(value: number): string {
    if (value >= 1000) {
      const thousands = (value / 1000).toFixed(2);
      return thousands.replace('.', ',') + 'K';
    }
    return value.toString();
  }

  loadCampaignStats(): void {
  this.dashboardService.getCampaignStats().subscribe({
    next: (data) => {
      console.log('Campaign Stats Response:', data);
      if (data && Array.isArray(data) && data.length > 0) {
        const validData = data.filter(
          (item: any) =>
            item['[Compaigns].[Campaign].[Campaign].[MEMBER_CAPTION]'] &&
            item['[Measures].[Nb Sent]'] != null &&
            item['[Measures].[Nb Deliv]'] != null
        );

        const labels = validData.map((item: any) => {
          const label = item['[Compaigns].[Campaign].[Campaign].[MEMBER_CAPTION]'];

          // Log the label to see the actual value
          console.log('Campaign Label:', label);

          // Remove 'Unknown' and replace 'object' with 'Total'
          if (label && typeof label === 'object') {
            return 'Total'; // Replace object with 'Total'
          } else if (typeof label === 'string') {
            if (label.toLowerCase() === 'object') {
              return 'Total'; // Replace 'object' string with 'Total'
            }
            if (label.toLowerCase() === 'unknown') {
              return ''; // Remove the 'Unknown' label by returning an empty string
            }
          }

          // Return the label for other cases
          return label;
        }).filter((label) => label !== ''); // Filter out empty labels (i.e., 'Unknown')

        const nbSentData = validData.map((item: any) => item['[Measures].[Nb Sent]']);
        const nbDelivData = validData.map((item: any) => item['[Measures].[Nb Deliv]']);

        this.campaignChartData = {
          labels: labels,
          datasets: [
            { data: nbSentData, label: 'Nb Sent', backgroundColor: '#8e24aa' }, // purple
            { data: nbDelivData, label: 'Nb Deliv', backgroundColor: '#1E88E5' }
          ]
        };
      } else {
        console.warn('No campaign stats data available');
      }
    },
    error: (err) => {
      console.error('Error fetching campaign stats:', err);
    }
  });
}



  loadSpendByGender(): void {
    this.dashboardService.getSpendByGender().subscribe({
      next: (data) => {
        console.log('Spend by Gender Response:', data);
        if (data && Array.isArray(data) && data.length > 0) {
          // Filter out invalid entries (non-string gender or non-numeric spend)
         const validData = data.filter(
  (item: any) =>
    typeof item['[Genders].[Gender].[Gender].[MEMBER_CAPTION]'] === 'string' &&
    item['[Genders].[Gender].[Gender].[MEMBER_CAPTION]'] !== 'Unknown' &&
    typeof item['[Measures].[Spend]'] === 'number'
);

          const labels = validData.map((item: any) => item['[Genders].[Gender].[Gender].[MEMBER_CAPTION]'] || 'Unknown');
          const spendData = validData.map((item: any) => item['[Measures].[Spend]'] || 0);

          this.spendByGenderChartData = {
            labels: labels,
            datasets: [
              { data: spendData, label: 'Spend', backgroundColor: '#42A5F5' }
            ]
          };
        } else {
          console.warn('No spend by gender data available');
        }
      },
      error: (err) => {
        console.error('Error fetching spend by gender:', err);
      }
    });
  }

  loadKPIs(): void {
    this.dashboardService.getNbSent().subscribe({
      next: (data) => {
        console.log('NbSent Response:', data);
        const value = Array.isArray(data) && data.length > 0 ? data[0]['[Measures].[Nb Sent]'] : null;
        this.nbSent = value != null ? this.formatNumber(value) : 'N/A';
      },
      error: (err) => {
        console.error('Error fetching NbSent:', err);
        this.nbSent = 'N/A';
      }
    });

    this.dashboardService.getTotalNbDeliv().subscribe({
      next: (data) => {
        console.log('TotalNbDeliv Response:', data);
        const value = Array.isArray(data) && data.length > 0 ? data[0]['[Measures].[Nb Deliv]'] : null;
        this.nbDeliv = value != null ? this.formatNumber(value) : 'N/A';
      },
      error: (err) => {
        console.error('Error fetching NbDeliv:', err);
        this.nbDeliv = 'N/A';
      }
    });

    this.dashboardService.getDeliveryRate().subscribe({
      next: (data) => {
        console.log('DeliveryRate Response:', data);
        const value = Array.isArray(data) && data.length > 0 
          ? (data[0]['[Measures].[Delivery Rate]'] || data[0]['[Measures].[DeliveryRate]']) 
          : null;
        this.deliveryRate = value != null ? (value * 100).toFixed(2) + '%' : 'N/A';
      },
      error: (err) => {
        console.error('Error fetching DeliveryRate:', err);
        this.deliveryRate = 'N/A';
      }
    });

    this.dashboardService.getOpenRate().subscribe({
      next: (data) => {
        console.log('OpenRate Response:', data);
        const value = Array.isArray(data) && data.length > 0 
          ? (data[0]['[Measures].[Open Rate]'] || data[0]['[Measures].[OpenRate]']) 
          : null;
        this.openRate = value != null ? (value * 100).toFixed(2) + '%' : 'N/A';
      },
      error: (err) => {
        console.error('Error fetching OpenRate:', err);
        this.openRate = 'N/A';
      }
    });

    this.dashboardService.getVisited().subscribe({
      next: (data) => {
        console.log('Visited Response:', data);
        const value = Array.isArray(data) && data.length > 0 ? data[0]['[Measures].[Visited]'] : null;
        this.visited = value != null ? this.formatNumber(value) : 'N/A';
      },
      error: (err) => {
        console.error('Error fetching Visited:', err);
        this.visited = 'N/A';
      }
    });

    this.dashboardService.getTotalSpend().subscribe({
      next: (data) => {
        console.log('TotalSpend Response:', data);
        const value = Array.isArray(data) && data.length > 0 ? data[0]['[Measures].[Spend]'] : null;
        this.spend = value != null ? this.formatNumber(value) : 'N/A';
      },
      error: (err) => {
        console.error('Error fetching Spend:', err);
        this.spend = 'N/A';
      }
    });
  }
  logout() {
    this.authService.logout();
    this.router.navigate(['/login']);
  }
}
