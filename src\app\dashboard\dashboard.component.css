.sidenav-container {
  height: 100vh;
}

.sidenav {
  width: 240px;
  background: linear-gradient(180deg, #4a90e2 0%, #d3e0ea 100%); /* Lighter blue-gray gradient */
  color: #333;
  transition: width 0.3s ease;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);

  &.sidenav-collapsed {
    width: 60px;
  }
}

.sidenav-header {
  padding: 1rem;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.sidenav-logo {
  max-width: 100px;
  height: auto;
  transition: max-width 0.3s ease;

  &.logo-collapsed {
    max-width: 40px;
  }
}

mat-nav-list {
  padding-top: 1rem;

  a {
    color: #333;
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    font-size: 1rem;

    &:hover {
      background: rgba(74, 144, 226, 0.2);
      border-left-color: #4a90e2;
    }

    &.active {
      background: rgba(74, 144, 226, 0.3);
      border-left-color: #4a90e2;
      color: #fff;
    }

    mat-icon {
      margin-right: 1rem;
      color: #ffffff;
    }
  }

  .logout-item {
    position: absolute;
    bottom: 1rem;
    width: calc(100% - 2rem);
    margin: 0 1rem;

    &:hover {
      background: rgba(231, 76, 60, 0.2);
      border-left-color: #e74c3c;
    }

    mat-icon {
      color: #e74c3c;
    }
  }
}

.nav-text {
  flex: 1;
}

.sidenav-collapsed {
  .nav-text {
    display: none;
  }

  mat-icon {
    margin-right: 0;
  }
}

.dashboard-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

mat-toolbar {
  flex-shrink: 0;
  background: #4a90e2;
  color: #fff;
}

.content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #f5f7fa;
}

.chart-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chart-container {
  width: 100%;
  height: 400px;
}

.chart-container canvas {
  width: 100% !important;
  height: 100% !important;
}

.kpi-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.kpi-card {
  flex: 1 1 calc(16.66% - 20px);
  min-width: 150px;
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  padding: 10px;

  &:hover {
    transform: translateY(-5px);
  }

  mat-icon {
    font-size: 1.5rem;
    color: #4a90e2;
    margin-bottom: 0.5rem;
  }
}

mat-card-title {
  font-size: 16px;
  margin-bottom: 10px;
  color: #333;
}

mat-card-content {
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
}