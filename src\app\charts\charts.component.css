.sidenav-container {
  height: 100vh;
}

.sidenav {
  width: 240px;
  background: linear-gradient(180deg, #4a90e2 0%, #d3e0ea 100%);
  color: #333;
  transition: width 0.3s ease;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);

  &.sidenav-collapsed {
    width: 60px;
  }
}

.sidenav-header {
  padding: 1rem;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.sidenav-logo {
  max-width: 100px;
  height: auto;
  transition: max-width 0.3s ease;

  &.logo-collapsed {
    max-width: 40px;
  }
}

mat-nav-list {
  padding-top: 1rem;

  a {
    color: #333;
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    font-size: 1rem;

    &:hover {
      background: rgba(74, 144, 226, 0.2);
      border-left-color: #4a90e2;
    }

    &.active {
      background: rgba(74, 144, 226, 0.3);
      border-left-color: #4a90e2;
      color: #fff;
    }

    mat-icon {
      margin-right: 1rem;
      color: #ffffff;
    }
  }

  .logout-item {
    position: absolute;
    bottom: 1rem;
    width: calc(100% - 2rem);
    margin: 0 1rem;

    &:hover {
      background: rgba(231, 76, 60, 0.2);
      border-left-color: #e74c3c;
    }

    mat-icon {
      color: #e74c3c;
    }
  }
}

.nav-text {
  flex: 1;
}

.sidenav-collapsed {
  .nav-text {
    display: none;
  }

  mat-icon {
    margin-right: 0;
  }
}

.dashboard-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.toolbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background: #4a90e2;
  color: #fff;
}

.content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.chart-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  }
}

mat-card-title {
  font-size: 18px;
  font-weight: 600;
}

.chart-container {
  width: 100%;
  height: 300px;
  padding: 16px;
}

.chart-container canvas {
  width: 100% !important;
  height: 100% !important;
}