<div class="login-container">
  <!-- Left Side - Image/Branding -->
  <div class="login-left">
    <div class="brand-content">
      <div class="brand-logo">
        <img src="assets/logo.png" alt="App Logo" />
      </div>
      <h1 class="brand-title">Welcome to Dashboard</h1>
      <p class="brand-subtitle">
        Experience the power of modern analytics and data visualization
      </p>
      <div class="features-list">
        <div class="feature-item">
          <i class="fas fa-chart-line"></i>
          <span>Advanced Analytics</span>
        </div>
        <div class="feature-item">
          <i class="fas fa-shield-alt"></i>
          <span>Secure & Reliable</span>
        </div>
        <div class="feature-item">
          <i class="fas fa-users"></i>
          <span>Team Collaboration</span>
        </div>
      </div>
    </div>
    <div class="decorative-elements">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
    </div>
  </div>

  <!-- Right Side - Login Form -->
  <div class="login-right">
    <div class="login-form-container">
      <div class="form-header">
        <h2>Sign In</h2>
        <p>Enter your credentials to access your account</p>
      </div>

      <form (ngSubmit)="onSubmit()" class="login-form" #loginForm="ngForm">
        <div class="input-group">
          <div class="input-wrapper">
            <i class="fas fa-envelope input-icon"></i>
            <input
              id="username"
              type="text"
              [(ngModel)]="username"
              name="username"
              placeholder=" "
              required
              #usernameInput="ngModel"
              [ngClass]="{
                invalid:
                  usernameInput.invalid &&
                  (usernameInput.dirty || usernameInput.touched),
                filled: username
              }"
            />
            <label for="username">Email or Username</label>
          </div>
          <span
            class="error-message"
            *ngIf="
              usernameInput.invalid &&
              (usernameInput.dirty || usernameInput.touched)
            "
          >
            <i class="fas fa-exclamation-circle"></i>
            This field is required
          </span>
        </div>

        <div class="input-group">
          <div class="input-wrapper password-wrapper">
            <i class="fas fa-lock input-icon"></i>
            <input
              id="password"
              [type]="showPassword ? 'text' : 'password'"
              [(ngModel)]="password"
              name="password"
              placeholder=" "
              required
              #passwordInput="ngModel"
              [ngClass]="{
                invalid:
                  passwordInput.invalid &&
                  (passwordInput.dirty || passwordInput.touched),
                filled: password
              }"
            />
            <label for="password">Password</label>
            <button
              type="button"
              class="toggle-password"
              (click)="togglePassword()"
              aria-label="Toggle password visibility"
            >
              <i
                class="fas"
                [ngClass]="showPassword ? 'fa-eye-slash' : 'fa-eye'"
              ></i>
            </button>
          </div>
          <span
            class="error-message"
            *ngIf="
              passwordInput.invalid &&
              (passwordInput.dirty || passwordInput.touched)
            "
          >
            <i class="fas fa-exclamation-circle"></i>
            Password is required
          </span>
        </div>

        <button
          type="submit"
          class="login-btn"
          [disabled]="loginForm.invalid || isLoading"
        >
          <span *ngIf="!isLoading" class="btn-content">
            <i class="fas fa-arrow-right"></i>
            Sign In
          </span>
          <span *ngIf="isLoading" class="loading-content">
            <div class="spinner"></div>
            Signing in...
          </span>
        </button>

        <div class="error-container" *ngIf="error">
          <div class="error-message global-error">
            <i class="fas fa-exclamation-triangle"></i>
            {{ error }}
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
