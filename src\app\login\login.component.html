<div class="login-background">
  <div class="login-container">
    <div class="login-card">
      <div class="card-header">
        <div class="logo">
          <img src="assets/logo.png" alt="App Logo" />
        </div>
        <h2>Welcome Back</h2>
        <p class="subtitle">Sign in to your account</p>
      </div>

      <form (ngSubmit)="onSubmit()" class="login-form" #loginForm="ngForm">
        <div class="input-group">
          <div class="input-wrapper">
            <i class="fas fa-user input-icon"></i>
            <input
              id="username"
              type="text"
              [(ngModel)]="username"
              name="username"
              placeholder="Username"
              required
              #usernameInput="ngModel"
              [ngClass]="{
                invalid:
                  usernameInput.invalid &&
                  (usernameInput.dirty || usernameInput.touched),
                filled: username
              }"
            />
            <label for="username">Username</label>
          </div>
          <span
            class="error-message"
            *ngIf="
              usernameInput.invalid &&
              (usernameInput.dirty || usernameInput.touched)
            "
          >
            <i class="fas fa-exclamation-circle"></i>
            Username is required
          </span>
        </div>

        <div class="input-group">
          <div class="input-wrapper password-wrapper">
            <i class="fas fa-lock input-icon"></i>
            <input
              id="password"
              [type]="showPassword ? 'text' : 'password'"
              [(ngModel)]="password"
              name="password"
              placeholder="Password"
              required
              #passwordInput="ngModel"
              [ngClass]="{
                invalid:
                  passwordInput.invalid &&
                  (passwordInput.dirty || passwordInput.touched),
                filled: password
              }"
            />
            <label for="password">Password</label>
            <button
              type="button"
              class="toggle-password"
              (click)="togglePassword()"
              aria-label="Toggle password visibility"
            >
              <i
                class="fas"
                [ngClass]="showPassword ? 'fa-eye-slash' : 'fa-eye'"
              ></i>
            </button>
          </div>
          <span
            class="error-message"
            *ngIf="
              passwordInput.invalid &&
              (passwordInput.dirty || passwordInput.touched)
            "
          >
            <i class="fas fa-exclamation-circle"></i>
            Password is required
          </span>
        </div>

        <div class="form-options">
          <label class="checkbox-container">
            <input type="checkbox" [(ngModel)]="rememberMe" name="rememberMe" />
            <span class="checkmark"></span>
            Remember me
          </label>
          <a href="#" class="forgot-password">Forgot password?</a>
        </div>

        <button
          type="submit"
          class="login-btn"
          [disabled]="loginForm.invalid || isLoading"
        >
          <span *ngIf="!isLoading" class="btn-content">
            <i class="fas fa-sign-in-alt"></i>
            Sign In
          </span>
          <span *ngIf="isLoading" class="loading-content">
            <div class="spinner"></div>
            Signing in...
          </span>
        </button>

        <div class="divider">
          <span>or</span>
        </div>

        <div class="social-login">
          <button type="button" class="social-btn google-btn">
            <i class="fab fa-google"></i>
            Continue with Google
          </button>
          <button type="button" class="social-btn microsoft-btn">
            <i class="fab fa-microsoft"></i>
            Continue with Microsoft
          </button>
        </div>
      </form>

      <div class="error-container" *ngIf="error">
        <div class="error-message global-error">
          <i class="fas fa-exclamation-triangle"></i>
          {{ error }}
        </div>
      </div>

      <div class="card-footer">
        <p>
          Don't have an account? <a href="#" class="signup-link">Sign up</a>
        </p>
      </div>
    </div>
  </div>
</div>
