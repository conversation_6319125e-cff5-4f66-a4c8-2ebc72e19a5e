<div class="login-container">
  <div class="logo">
    <img src="assets/logo.png" alt="App Logo" />
  </div>
  <h2>Login to Your Account</h2>
  <form (ngSubmit)="onSubmit()" class="login-form" #loginForm="ngForm">
    <div class="input-group">
      <label for="username">Username</label>
      <input
        id="username"
        type="text"
        [(ngModel)]="username"
        name="username"
        placeholder="Enter username"
        required
        #usernameInput="ngModel"
        [ngClass]="{'invalid': usernameInput.invalid && (usernameInput.dirty || usernameInput.touched)}"
      />
      <span class="error" *ngIf="usernameInput.invalid && (usernameInput.dirty || usernameInput.touched)">
        Username is required
      </span>
    </div>
    <div class="input-group">
      <label for="password">Password</label>
      <div class="password-wrapper">
        <input
          id="password"
          [type]="showPassword ? 'text' : 'password'"
          [(ngModel)]="password"
          name="password"
          placeholder="Enter password"
          required
          #passwordInput="ngModel"
          [ngClass]="{'invalid': passwordInput.invalid && (passwordInput.dirty || passwordInput.touched)}"
        />
        <button
          type="button"
          class="toggle-password"
          (click)="togglePassword()"
          aria-label="Toggle password visibility"
        >
          <i class="fas" [ngClass]="showPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
        </button>
      </div>
      <span class="error" *ngIf="passwordInput.invalid && (passwordInput.dirty || passwordInput.touched)">
        Password is required
      </span>
    </div>
    <button type="submit" [disabled]="loginForm.invalid || isLoading">
      <span *ngIf="!isLoading">Login</span>
      <span *ngIf="isLoading" class="spinner"></span>
    </button>
  </form>
  <p class="error" *ngIf="error">{{ error }}</p>
</div>