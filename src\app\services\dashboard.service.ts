import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  private baseUrl = 'http://localhost:5168/api/dashboard'; // Your backend API URL

  constructor(private http: HttpClient) {}

  getCampaignStats(): Observable<any> {
    return this.http.get(`${this.baseUrl}/campaign-stats`);
  }

  getSpendByGender(): Observable<any> {
    return this.http.get(`${this.baseUrl}/SpendByGender`);
  }

  getTotalSpend(): Observable<any> {
    return this.http.get(`${this.baseUrl}/TotalSpend`);
  }

  getNbSent(): Observable<any> {
    return this.http.get(`${this.baseUrl}/NbSent`);
  }

  getTotalNbDeliv(): Observable<any> {
    return this.http.get(`${this.baseUrl}/TotalNbDeliv`);
  }

  getDeliveryRate(): Observable<any> {
    return this.http.get(`${this.baseUrl}/DeliveryRate`);
  }

  getOpenRate(): Observable<any> {
    return this.http.get(`${this.baseUrl}/OpenRate`);
  }

  getVisited(): Observable<any> {
    return this.http.get(`${this.baseUrl}/Visited`);
  }
  getOpenRateByCampaign() : Observable<any> {
    return this.http.get(`${this.baseUrl}/OpenRateByCampaign`);
  }
  
  getDeliveryRateByGender() : Observable<any> {
    return this.http.get(`${this.baseUrl}/DeliveryRateByGender`);
  }
  
  getNbSpendByAge() : Observable<any>{
    return this.http.get(`${this.baseUrl}/NbSpendByAge`);
  }
  GetTODEOperAgeandDOWEO() : Observable<any>{
    return this.http.get(`${this.baseUrl}/TODEOperAgeandDOWEO`);
  }
  GetTODEOAGE() : Observable<any>{
    return this.http.get(`${this.baseUrl}/TODEOAGE`);
  }
}
